using ColetaAPI.DataContext;
using ColetaAPI.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;


namespace ColetaAPI.Service.ColetaService
{
    public class ColetaService : IColetaInterface
    {
        // Dependency Injection
        private readonly ApplicationsDbContext _context;

        // Constructor
        public ColetaService(ApplicationsDbContext context)
        {
            _context = context;
        }
        public async Task<ServiceResponse<List<ColetaModel>>> AddColeta(ColetaModel coleta)
        {
            ServiceResponse<List<ColetaModel>> ServiceResponse = new ServiceResponse<List<ColetaModel>>();
            try
            {
                if(coleta.Local == null)
                {
                    ServiceResponse.Data = null;
                    ServiceResponse.Message = "Local é obrigatório";
                    ServiceResponse.Success = false;
                    
                    return ServiceResponse;
                }
                _context.Add(coleta);
                await _context.SaveChangesAsync();
                ServiceResponse.Data = await _context.Coletas.ToListAsync();
            }
            catch (Exception ex)
            {
                ServiceResponse.Success = false;
                ServiceResponse.Message = ex.Message;
            }
            return ServiceResponse;
        }

        public async Task<ServiceResponse<List<ColetaModel>>> DeleteColeta(int ID)
        {
            ServiceResponse<List<ColetaModel>> ServiceResponse = new ServiceResponse<List<ColetaModel>>();
            try
            {
                if (coleta.ID == null)
                {
                    throw new Exception("Coleta não encontrada");
                }
                ColetaModel coleta = await _context.Coletas.ToListAsync();
                _context.Remove(coleta);
                await _context.SaveChangesAsync();
                ServiceResponse.Data = await _context.Coletas.ToListAsync();
            }
            catch (Exception ex)
            {
                ServiceResponse.Success = false;
                ServiceResponse.Message = ex.Message;
            }
            return ServiceResponse;
        }

        public async Task<ServiceResponse<List<ColetaModel>>> GetColeta()
        {
            ServiceResponse<List<ColetaModel>> ServiceResponse = new ServiceResponse<List<ColetaModel>>();
            try
            {
                ServiceResponse.Data = await _context.Coletas.ToListAsync();
            }
            catch (Exception ex)
            {
                ServiceResponse.Success = false;
                ServiceResponse.Message = ex.Message;

                if (ServiceResponse.Data == null)
                {
                    ServiceResponse.Message = "Nenhum dado encontrado";
                }
            }
            return ServiceResponse;
        }

        public Task<ServiceResponse<ColetaModel>> GetSingleColeta(int id)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResponse<List<ColetaModel>>> PostColetas(List<ColetaModel> coletas)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResponse<ColetaModel>> UpdateColeta(ColetaModel coleta)
        {
            throw new NotImplementedException();
        }
    }
}
